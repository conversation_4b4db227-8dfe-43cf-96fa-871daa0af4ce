import type { Metadata } from "next";
import { DM_Sans } from "next/font/google";
import localFont from "next/font/local";
import "./globals.css";

// Fallback to DM Sans (similar to Aeonik)
const dmSans = DM_Sans({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  variable: "--font-dm-sans",
  display: "swap",
});

// Custom Aeonik font (fallback to DM Sans if not available)
const aeonik = localFont({
  src: [
    {
      path: "../../../public/fonts/Aeonik-Medium.woff2",
      weight: "500",
      style: "normal",
    },
  ],
  variable: "--font-aeonik",
  display: "swap",
  fallback: ["var(--font-dm-sans)", "system-ui", "sans-serif"],
});

export const metadata: Metadata = {
  title: "OI - Digital Experiences",
  description: "We help brands create digital experiences that connect with their audience",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${dmSans.variable} ${aeonik.variable}`}>
      <body className="antialiased bg-white text-black" style={{ fontFamily: 'var(--font-dm-sans), system-ui, sans-serif' }}>
        {children}
      </body>
    </html>
  );
}
