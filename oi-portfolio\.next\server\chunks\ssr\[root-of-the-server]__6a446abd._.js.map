{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/oi-portfolio/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { motion, useScroll, useTransform } from 'framer-motion';\nimport { gsap } from 'gsap';\n\nconst Hero = () => {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const textRef = useRef<HTMLDivElement>(null);\n  const { scrollYProgress } = useScroll({\n    target: containerRef,\n    offset: ['start start', 'end start']\n  });\n\n  const y = useTransform(scrollYProgress, [0, 1], ['0%', '50%']);\n  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);\n\n  useEffect(() => {\n    if (textRef.current) {\n      const chars = textRef.current.querySelectorAll('.char');\n      \n      gsap.fromTo(chars, \n        { \n          y: 100,\n          opacity: 0,\n          rotationX: -90\n        },\n        {\n          y: 0,\n          opacity: 1,\n          rotationX: 0,\n          duration: 1.2,\n          stagger: 0.02,\n          ease: 'power3.out',\n          delay: 0.5\n        }\n      );\n    }\n  }, []);\n\n  const splitText = (text: string) => {\n    return text.split('').map((char, index) => (\n      <span key={index} className=\"char inline-block\" style={{ transformOrigin: '50% 100%' }}>\n        {char === ' ' ? '\\u00A0' : char}\n      </span>\n    ));\n  };\n\n  return (\n    <motion.section\n      ref={containerRef}\n      className=\"relative h-screen flex items-center justify-center overflow-hidden\"\n      style={{ y, opacity }}\n    >\n      {/* Background gradient */}\n      <div className=\"absolute inset-0 bg-gradient-to-b from-white via-gray-50 to-white\" />\n      \n      {/* Main content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-6 text-center\">\n        <motion.div\n          ref={textRef}\n          className=\"mb-8\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n        >\n          <h1 className=\"text-6xl md:text-8xl lg:text-9xl font-bold tracking-tight leading-none mb-6\">\n            {splitText('OI')}\n          </h1>\n          \n          <motion.p\n            className=\"text-xl md:text-2xl lg:text-3xl font-light max-w-4xl mx-auto leading-relaxed\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1.2 }}\n          >\n            We help brands create digital experiences that connect with their audience\n          </motion.p>\n        </motion.div>\n\n        {/* Scroll indicator */}\n        <motion.div\n          className=\"absolute bottom-12 left-1/2 transform -translate-x-1/2\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 2 }}\n        >\n          <div className=\"flex flex-col items-center space-y-2\">\n            <span className=\"text-sm font-medium tracking-wider\">SCROLL TO EXPLORE</span>\n            <motion.div\n              className=\"w-px h-12 bg-black\"\n              animate={{ scaleY: [1, 0.5, 1] }}\n              transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}\n            />\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Decorative elements */}\n      <motion.div\n        className=\"absolute top-1/4 left-12 w-2 h-2 bg-black rounded-full\"\n        animate={{ \n          scale: [1, 1.5, 1],\n          opacity: [0.3, 1, 0.3]\n        }}\n        transition={{ duration: 3, repeat: Infinity, ease: 'easeInOut' }}\n      />\n      \n      <motion.div\n        className=\"absolute top-1/3 right-16 w-1 h-1 bg-black rounded-full\"\n        animate={{ \n          scale: [1, 2, 1],\n          opacity: [0.5, 1, 0.5]\n        }}\n        transition={{ duration: 4, repeat: Infinity, ease: 'easeInOut', delay: 1 }}\n      />\n      \n      <motion.div\n        className=\"absolute bottom-1/4 left-1/4 w-1.5 h-1.5 bg-black rounded-full\"\n        animate={{ \n          scale: [1, 1.8, 1],\n          opacity: [0.4, 1, 0.4]\n        }}\n        transition={{ duration: 2.5, repeat: Infinity, ease: 'easeInOut', delay: 0.5 }}\n      />\n    </motion.section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,OAAO;IACX,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACvC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAe;SAAY;IACtC;IAEA,MAAM,IAAI,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAM;KAAM;IAC7D,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAI,EAAE;QAAC;QAAG;KAAE;IAE9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,OAAO,EAAE;YACnB,MAAM,QAAQ,QAAQ,OAAO,CAAC,gBAAgB,CAAC;YAE/C,6IAAA,CAAA,OAAI,CAAC,MAAM,CAAC,OACV;gBACE,GAAG;gBACH,SAAS;gBACT,WAAW,CAAC;YACd,GACA;gBACE,GAAG;gBACH,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,SAAS;gBACT,MAAM;gBACN,OAAO;YACT;QAEJ;IACF,GAAG,EAAE;IAEL,MAAM,YAAY,CAAC;QACjB,OAAO,KAAK,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM,sBAC/B,8OAAC;gBAAiB,WAAU;gBAAoB,OAAO;oBAAE,iBAAiB;gBAAW;0BAClF,SAAS,MAAM,WAAW;eADlB;;;;;IAIf;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,WAAU;QACV,OAAO;YAAE;YAAG;QAAQ;;0BAGpB,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,KAAK;wBACL,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,8OAAC;gCAAG,WAAU;0CACX,UAAU;;;;;;0CAGb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CACzC;;;;;;;;;;;;kCAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAE;kCAEtC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAqC;;;;;;8CACrD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,QAAQ;4CAAC;4CAAG;4CAAK;yCAAE;oCAAC;oCAC/B,YAAY;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAY;;;;;;;;;;;;;;;;;;;;;;;0BAOvE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;gBACxB;gBACA,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAY;;;;;;0BAGjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAG;qBAAE;oBAChB,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;gBACxB;gBACA,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;oBAAa,OAAO;gBAAE;;;;;;0BAG3E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;gBACxB;gBACA,YAAY;oBAAE,UAAU;oBAAK,QAAQ;oBAAU,MAAM;oBAAa,OAAO;gBAAI;;;;;;;;;;;;AAIrF;uCAEe", "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/oi-portfolio/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst Navigation = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const menuItems = [\n    { name: 'Work', href: '#work' },\n    { name: 'About', href: '#about' },\n    { name: 'Contact', href: '#contact' },\n  ];\n\n  return (\n    <>\n      <motion.nav\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          scrolled ? 'bg-white/80 backdrop-blur-md' : 'bg-transparent'\n        }`}\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        transition={{ duration: 0.6, ease: 'easeOut' }}\n      >\n        <div className=\"max-w-7xl mx-auto px-6 py-6 flex items-center justify-between\">\n          {/* Logo */}\n          <motion.div\n            className=\"text-2xl font-bold tracking-tight\"\n            whileHover={{ scale: 1.05 }}\n            transition={{ type: 'spring', stiffness: 400, damping: 10 }}\n          >\n            OI\n          </motion.div>\n\n          {/* Desktop Menu */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {menuItems.map((item) => (\n              <motion.a\n                key={item.name}\n                href={item.href}\n                className=\"text-sm font-medium hover:text-gray-600 transition-colors\"\n                whileHover={{ y: -2 }}\n                transition={{ type: 'spring', stiffness: 400, damping: 10 }}\n              >\n                {item.name}\n              </motion.a>\n            ))}\n          </div>\n\n          {/* Let's Talk Button */}\n          <motion.button\n            className=\"hidden md:flex items-center space-x-2 bg-black text-white px-6 py-3 rounded-full text-sm font-medium hover:bg-gray-800 transition-colors\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            transition={{ type: 'spring', stiffness: 400, damping: 10 }}\n          >\n            <span>LET'S TALK</span>\n            <svg\n              width=\"16\"\n              height=\"16\"\n              viewBox=\"0 0 16 16\"\n              fill=\"none\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n            >\n              <path\n                d=\"M8 1L15 8L8 15M15 8H1\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n              />\n            </svg>\n          </motion.button>\n\n          {/* Mobile Menu Button */}\n          <motion.button\n            className=\"md:hidden flex flex-col items-center justify-center w-8 h-8 space-y-1\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            whileTap={{ scale: 0.95 }}\n          >\n            <motion.span\n              className=\"w-6 h-0.5 bg-black transition-all\"\n              animate={{\n                rotate: isMenuOpen ? 45 : 0,\n                y: isMenuOpen ? 6 : 0,\n              }}\n            />\n            <motion.span\n              className=\"w-6 h-0.5 bg-black transition-all\"\n              animate={{\n                opacity: isMenuOpen ? 0 : 1,\n              }}\n            />\n            <motion.span\n              className=\"w-6 h-0.5 bg-black transition-all\"\n              animate={{\n                rotate: isMenuOpen ? -45 : 0,\n                y: isMenuOpen ? -6 : 0,\n              }}\n            />\n          </motion.button>\n        </div>\n      </motion.nav>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMenuOpen && (\n          <motion.div\n            className=\"fixed inset-0 z-40 bg-white md:hidden\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            <div className=\"flex flex-col items-center justify-center h-full space-y-8\">\n              {menuItems.map((item, index) => (\n                <motion.a\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-2xl font-medium\"\n                  onClick={() => setIsMenuOpen(false)}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                >\n                  {item.name}\n                </motion.a>\n              ))}\n              <motion.button\n                className=\"bg-black text-white px-8 py-4 rounded-full text-lg font-medium\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                LET'S TALK\n              </motion.button>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKA,MAAM,aAAa;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE;;0BACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAC,4DAA4D,EACtE,WAAW,iCAAiC,kBAC5C;gBACF,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAU;0BAE7C,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,YAAY;gCAAE,MAAM;gCAAU,WAAW;gCAAK,SAAS;4BAAG;sCAC3D;;;;;;sCAKD,8OAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,YAAY;wCAAE,MAAM;wCAAU,WAAW;wCAAK,SAAS;oCAAG;8CAEzD,KAAK,IAAI;mCANL,KAAK,IAAI;;;;;;;;;;sCAYpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,YAAY;gCAAE,MAAM;gCAAU,WAAW;gCAAK,SAAS;4BAAG;;8CAE1D,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCACC,OAAM;oCACN,QAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,OAAM;8CAEN,cAAA,8OAAC;wCACC,GAAE;wCACF,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;;;;;;;;;;;;;;;;sCAMrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;4BAC9B,UAAU;gCAAE,OAAO;4BAAK;;8CAExB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,WAAU;oCACV,SAAS;wCACP,QAAQ,aAAa,KAAK;wCAC1B,GAAG,aAAa,IAAI;oCACtB;;;;;;8CAEF,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,WAAU;oCACV,SAAS;wCACP,SAAS,aAAa,IAAI;oCAC5B;;;;;;8CAEF,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,WAAU;oCACV,SAAS;wCACP,QAAQ,aAAa,CAAC,KAAK;wCAC3B,GAAG,aAAa,CAAC,IAAI;oCACvB;;;;;;;;;;;;;;;;;;;;;;;0BAOR,8OAAC,yLAAA,CAAA,kBAAe;0BACb,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;8CAEhC,KAAK,IAAI;mCARL,KAAK,IAAI;;;;;0CAWlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;0CAC1B;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/oi-portfolio/src/components/Scene3D.tsx"], "sourcesContent": ["'use client';\n\nimport { Suspense, useRef, useMemo } from 'react';\nimport { <PERSON><PERSON>, use<PERSON>rame, useThree } from '@react-three/fiber';\nimport { \n  OrbitControls, \n  Environment, \n  Float, \n  MeshTransmissionMaterial,\n  useTexture,\n  Sphere,\n  Box,\n  Cylinder\n} from '@react-three/drei';\nimport { Physics, RigidBody, CuboidCollider } from '@react-three/rapier';\nimport { <PERSON>Compose<PERSON>, <PERSON>, ToneMapping } from '@react-three/postprocessing';\nimport * as THREE from 'three';\nimport { useGesture } from '@use-gesture/react';\n\n// Interactive 3D Objects Component\nfunction Interactive3DObjects() {\n  const groupRef = useRef<THREE.Group>(null);\n  const { viewport, mouse } = useThree();\n  \n  useFrame((state) => {\n    if (groupRef.current) {\n      groupRef.current.rotation.y = mouse.x * 0.1;\n      groupRef.current.rotation.x = mouse.y * 0.05;\n    }\n  });\n\n  const objects = useMemo(() => {\n    const items = [];\n    for (let i = 0; i < 15; i++) {\n      items.push({\n        id: i,\n        position: [\n          (Math.random() - 0.5) * 8,\n          (Math.random() - 0.5) * 6,\n          (Math.random() - 0.5) * 4\n        ] as [number, number, number],\n        rotation: [\n          Math.random() * Math.PI,\n          Math.random() * Math.PI,\n          Math.random() * Math.PI\n        ] as [number, number, number],\n        scale: 0.3 + Math.random() * 0.7,\n        type: Math.floor(Math.random() * 3), // 0: sphere, 1: box, 2: cylinder\n        color: Math.random() > 0.5 ? '#000000' : '#ffffff'\n      });\n    }\n    return items;\n  }, []);\n\n  return (\n    <group ref={groupRef}>\n      {objects.map((obj) => (\n        <RigidBody key={obj.id} type=\"dynamic\" position={obj.position}>\n          <Float\n            speed={1 + Math.random() * 2}\n            rotationIntensity={0.5}\n            floatIntensity={0.5}\n          >\n            {obj.type === 0 && (\n              <Sphere args={[obj.scale]} castShadow receiveShadow>\n                <MeshTransmissionMaterial\n                  color={obj.color}\n                  thickness={0.5}\n                  roughness={0.1}\n                  transmission={0.9}\n                  ior={1.5}\n                  chromaticAberration={0.02}\n                  backside\n                />\n              </Sphere>\n            )}\n            {obj.type === 1 && (\n              <Box args={[obj.scale, obj.scale, obj.scale]} castShadow receiveShadow>\n                <meshStandardMaterial\n                  color={obj.color}\n                  metalness={0.8}\n                  roughness={0.2}\n                />\n              </Box>\n            )}\n            {obj.type === 2 && (\n              <Cylinder args={[obj.scale * 0.5, obj.scale * 0.5, obj.scale * 1.5]} castShadow receiveShadow>\n                <meshStandardMaterial\n                  color={obj.color}\n                  metalness={0.6}\n                  roughness={0.3}\n                />\n              </Cylinder>\n            )}\n          </Float>\n        </RigidBody>\n      ))}\n    </group>\n  );\n}\n\n// Mouse Follower Component\nfunction MouseFollower() {\n  const meshRef = useRef<THREE.Mesh>(null);\n  const { mouse, viewport } = useThree();\n  \n  useFrame(() => {\n    if (meshRef.current) {\n      meshRef.current.position.x = (mouse.x * viewport.width) / 2;\n      meshRef.current.position.y = (mouse.y * viewport.height) / 2;\n    }\n  });\n\n  return (\n    <mesh ref={meshRef} position={[0, 0, 2]}>\n      <sphereGeometry args={[0.1]} />\n      <meshBasicMaterial color=\"#ff0000\" transparent opacity={0.5} />\n    </mesh>\n  );\n}\n\n// Main Scene Component\nfunction Scene() {\n  return (\n    <>\n      <ambientLight intensity={0.4} />\n      <directionalLight\n        position={[10, 10, 5]}\n        intensity={1}\n        castShadow\n        shadow-mapSize-width={2048}\n        shadow-mapSize-height={2048}\n      />\n      <pointLight position={[-10, -10, -10]} intensity={0.5} />\n      \n      <Physics gravity={[0, -2, 0]}>\n        <Interactive3DObjects />\n        \n        {/* Invisible floor */}\n        <RigidBody type=\"fixed\" position={[0, -4, 0]}>\n          <CuboidCollider args={[10, 0.1, 10]} />\n        </RigidBody>\n      </Physics>\n      \n      <MouseFollower />\n      \n      <Environment preset=\"studio\" />\n      \n      <OrbitControls\n        enablePan={false}\n        enableZoom={false}\n        enableRotate={true}\n        autoRotate\n        autoRotateSpeed={0.5}\n        maxPolarAngle={Math.PI / 2}\n        minPolarAngle={Math.PI / 3}\n      />\n    </>\n  );\n}\n\n// Post-processing Effects\nfunction Effects() {\n  return (\n    <EffectComposer>\n      <Bloom\n        intensity={0.5}\n        luminanceThreshold={0.9}\n        luminanceSmoothing={0.9}\n      />\n      <ToneMapping adaptive />\n    </EffectComposer>\n  );\n}\n\n// Main Scene3D Component\nconst Scene3D = () => {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n\n  const bind = useGesture({\n    onMove: ({ xy: [x, y] }) => {\n      // Mouse movement effects can be added here\n    },\n    onHover: ({ hovering }) => {\n      if (canvasRef.current) {\n        canvasRef.current.style.cursor = hovering ? 'pointer' : 'default';\n      }\n    }\n  });\n\n  return (\n    <div \n      className=\"fixed inset-0 z-0\" \n      {...bind()}\n    >\n      <Canvas\n        ref={canvasRef}\n        shadows\n        camera={{ position: [0, 0, 8], fov: 50 }}\n        gl={{ \n          antialias: true,\n          alpha: true,\n          powerPreference: 'high-performance'\n        }}\n      >\n        <Suspense fallback={null}>\n          <Scene />\n          <Effects />\n        </Suspense>\n      </Canvas>\n    </div>\n  );\n};\n\nexport default Scene3D;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAUA;AAAA;AACA;AAEA;AAjBA;;;;;;;;AAmBA,mCAAmC;AACnC,SAAS;IACP,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IACrC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD;IAEnC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG;YACxC,SAAS,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG;QAC1C;IACF;IAEA,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACtB,MAAM,QAAQ,EAAE;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;YAC3B,MAAM,IAAI,CAAC;gBACT,IAAI;gBACJ,UAAU;oBACR,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBACxB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;iBACzB;gBACD,UAAU;oBACR,KAAK,MAAM,KAAK,KAAK,EAAE;oBACvB,KAAK,MAAM,KAAK,KAAK,EAAE;oBACvB,KAAK,MAAM,KAAK,KAAK,EAAE;iBACxB;gBACD,OAAO,MAAM,KAAK,MAAM,KAAK;gBAC7B,MAAM,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBACjC,OAAO,KAAK,MAAM,KAAK,MAAM,YAAY;YAC3C;QACF;QACA,OAAO;IACT,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAM,KAAK;kBACT,QAAQ,GAAG,CAAC,CAAC,oBACZ,8OAAC,qMAAA,CAAA,YAAS;gBAAc,MAAK;gBAAU,UAAU,IAAI,QAAQ;0BAC3D,cAAA,8OAAC,yJAAA,CAAA,QAAK;oBACJ,OAAO,IAAI,KAAK,MAAM,KAAK;oBAC3B,mBAAmB;oBACnB,gBAAgB;;wBAEf,IAAI,IAAI,KAAK,mBACZ,8OAAC,0JAAA,CAAA,SAAM;4BAAC,MAAM;gCAAC,IAAI,KAAK;6BAAC;4BAAE,UAAU;4BAAC,aAAa;sCACjD,cAAA,8OAAC,4KAAA,CAAA,2BAAwB;gCACvB,OAAO,IAAI,KAAK;gCAChB,WAAW;gCACX,WAAW;gCACX,cAAc;gCACd,KAAK;gCACL,qBAAqB;gCACrB,QAAQ;;;;;;;;;;;wBAIb,IAAI,IAAI,KAAK,mBACZ,8OAAC,0JAAA,CAAA,MAAG;4BAAC,MAAM;gCAAC,IAAI,KAAK;gCAAE,IAAI,KAAK;gCAAE,IAAI,KAAK;6BAAC;4BAAE,UAAU;4BAAC,aAAa;sCACpE,cAAA,8OAAC;gCACC,OAAO,IAAI,KAAK;gCAChB,WAAW;gCACX,WAAW;;;;;;;;;;;wBAIhB,IAAI,IAAI,KAAK,mBACZ,8OAAC,0JAAA,CAAA,WAAQ;4BAAC,MAAM;gCAAC,IAAI,KAAK,GAAG;gCAAK,IAAI,KAAK,GAAG;gCAAK,IAAI,KAAK,GAAG;6BAAI;4BAAE,UAAU;4BAAC,aAAa;sCAC3F,cAAA,8OAAC;gCACC,OAAO,IAAI,KAAK;gCAChB,WAAW;gCACX,WAAW;;;;;;;;;;;;;;;;;eAjCL,IAAI,EAAE;;;;;;;;;;AA0C9B;AAEA,2BAA2B;AAC3B,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACnC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD;IAEnC,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;QACP,IAAI,QAAQ,OAAO,EAAE;YACnB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,AAAC,MAAM,CAAC,GAAG,SAAS,KAAK,GAAI;YAC1D,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,AAAC,MAAM,CAAC,GAAG,SAAS,MAAM,GAAI;QAC7D;IACF;IAEA,qBACE,8OAAC;QAAK,KAAK;QAAS,UAAU;YAAC;YAAG;YAAG;SAAE;;0BACrC,8OAAC;gBAAe,MAAM;oBAAC;iBAAI;;;;;;0BAC3B,8OAAC;gBAAkB,OAAM;gBAAU,WAAW;gBAAC,SAAS;;;;;;;;;;;;AAG9D;AAEA,uBAAuB;AACvB,SAAS;IACP,qBACE;;0BACE,8OAAC;gBAAa,WAAW;;;;;;0BACzB,8OAAC;gBACC,UAAU;oBAAC;oBAAI;oBAAI;iBAAE;gBACrB,WAAW;gBACX,UAAU;gBACV,wBAAsB;gBACtB,yBAAuB;;;;;;0BAEzB,8OAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAI,CAAC;oBAAI,CAAC;iBAAG;gBAAE,WAAW;;;;;;0BAElD,8OAAC,qMAAA,CAAA,UAAO;gBAAC,SAAS;oBAAC;oBAAG,CAAC;oBAAG;iBAAE;;kCAC1B,8OAAC;;;;;kCAGD,8OAAC,qMAAA,CAAA,YAAS;wBAAC,MAAK;wBAAQ,UAAU;4BAAC;4BAAG,CAAC;4BAAG;yBAAE;kCAC1C,cAAA,8OAAC,qMAAA,CAAA,iBAAc;4BAAC,MAAM;gCAAC;gCAAI;gCAAK;6BAAG;;;;;;;;;;;;;;;;;0BAIvC,8OAAC;;;;;0BAED,8OAAC,+JAAA,CAAA,cAAW;gBAAC,QAAO;;;;;;0BAEpB,8OAAC,iKAAA,CAAA,gBAAa;gBACZ,WAAW;gBACX,YAAY;gBACZ,cAAc;gBACd,UAAU;gBACV,iBAAiB;gBACjB,eAAe,KAAK,EAAE,GAAG;gBACzB,eAAe,KAAK,EAAE,GAAG;;;;;;;;AAIjC;AAEA,0BAA0B;AAC1B,SAAS;IACP,qBACE,8OAAC,mKAAA,CAAA,iBAAc;;0BACb,8OAAC,mKAAA,CAAA,QAAK;gBACJ,WAAW;gBACX,oBAAoB;gBACpB,oBAAoB;;;;;;0BAEtB,8OAAC,mKAAA,CAAA,cAAW;gBAAC,QAAQ;;;;;;;;;;;;AAG3B;AAEA,yBAAyB;AACzB,MAAM,UAAU;IACd,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,MAAM,OAAO,CAAA,GAAA,mMAAA,CAAA,aAAU,AAAD,EAAE;QACtB,QAAQ,CAAC,EAAE,IAAI,CAAC,GAAG,EAAE,EAAE;QACrB,2CAA2C;QAC7C;QACA,SAAS,CAAC,EAAE,QAAQ,EAAE;YACpB,IAAI,UAAU,OAAO,EAAE;gBACrB,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,WAAW,YAAY;YAC1D;QACF;IACF;IAEA,qBACE,8OAAC;QACC,WAAU;QACT,GAAG,MAAM;kBAEV,cAAA,8OAAC,mMAAA,CAAA,SAAM;YACL,KAAK;YACL,OAAO;YACP,QAAQ;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBAAE,KAAK;YAAG;YACvC,IAAI;gBACF,WAAW;gBACX,OAAO;gBACP,iBAAiB;YACnB;sBAEA,cAAA,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,UAAU;;kCAClB,8OAAC;;;;;kCACD,8OAAC;;;;;;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 978, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/oi-portfolio/src/components/ScrollIndicator.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { motion, useScroll, useSpring } from 'framer-motion';\n\nconst ScrollIndicator = () => {\n  const [isVisible, setIsVisible] = useState(false);\n  const { scrollYProgress } = useScroll();\n  const scaleX = useSpring(scrollYProgress, {\n    stiffness: 100,\n    damping: 30,\n    restDelta: 0.001\n  });\n\n  useEffect(() => {\n    const handleScroll = () => {\n      const scrolled = window.scrollY;\n      const threshold = 100;\n      setIsVisible(scrolled > threshold);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n\n  return (\n    <>\n      {/* Progress bar */}\n      <motion.div\n        className=\"fixed top-0 left-0 right-0 h-1 bg-black origin-left z-50\"\n        style={{ scaleX }}\n      />\n\n      {/* Scroll to top button */}\n      <motion.button\n        className=\"fixed bottom-8 right-8 w-12 h-12 bg-black text-white rounded-full flex items-center justify-center z-40 hover:bg-gray-800 transition-colors\"\n        onClick={scrollToTop}\n        initial={{ opacity: 0, scale: 0 }}\n        animate={{ \n          opacity: isVisible ? 1 : 0,\n          scale: isVisible ? 1 : 0\n        }}\n        transition={{ duration: 0.3 }}\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n      >\n        <svg\n          width=\"16\"\n          height=\"16\"\n          viewBox=\"0 0 16 16\"\n          fill=\"none\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            d=\"M8 15V1M1 8L8 1L15 8\"\n            stroke=\"currentColor\"\n            strokeWidth=\"2\"\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n          />\n        </svg>\n      </motion.button>\n\n      {/* Side indicators */}\n      <div className=\"fixed left-8 top-1/2 transform -translate-y-1/2 z-40 hidden lg:block\">\n        <div className=\"flex flex-col space-y-4\">\n          {[1, 2, 3, 4].map((item) => (\n            <motion.div\n              key={item}\n              className=\"w-2 h-2 bg-black rounded-full cursor-pointer hover:scale-150 transition-transform\"\n              whileHover={{ scale: 1.5 }}\n              onClick={() => {\n                const section = document.getElementById(`section-${item}`);\n                if (section) {\n                  section.scrollIntoView({ behavior: 'smooth' });\n                }\n              }}\n            />\n          ))}\n        </div>\n      </div>\n\n      {/* Plus signs decoration */}\n      <div className=\"fixed inset-0 pointer-events-none z-10\">\n        <motion.div\n          className=\"absolute top-1/4 left-12 text-2xl font-light\"\n          animate={{ \n            rotate: [0, 90, 180, 270, 360],\n            opacity: [0.3, 1, 0.3]\n          }}\n          transition={{ duration: 8, repeat: Infinity, ease: 'linear' }}\n        >\n          +\n        </motion.div>\n        \n        <motion.div\n          className=\"absolute top-1/3 right-16 text-xl font-light\"\n          animate={{ \n            rotate: [360, 270, 180, 90, 0],\n            opacity: [0.5, 1, 0.5]\n          }}\n          transition={{ duration: 6, repeat: Infinity, ease: 'linear', delay: 2 }}\n        >\n          +\n        </motion.div>\n        \n        <motion.div\n          className=\"absolute bottom-1/4 left-1/4 text-lg font-light\"\n          animate={{ \n            rotate: [0, 180, 360],\n            opacity: [0.4, 1, 0.4]\n          }}\n          transition={{ duration: 4, repeat: Infinity, ease: 'linear', delay: 1 }}\n        >\n          +\n        </motion.div>\n        \n        <motion.div\n          className=\"absolute bottom-1/3 right-1/3 text-sm font-light\"\n          animate={{ \n            rotate: [360, 180, 0],\n            opacity: [0.6, 1, 0.6]\n          }}\n          transition={{ duration: 5, repeat: Infinity, ease: 'linear', delay: 3 }}\n        >\n          +\n        </motion.div>\n      </div>\n    </>\n  );\n};\n\nexport default ScrollIndicator;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,kBAAkB;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB;QACxC,WAAW;QACX,SAAS;QACT,WAAW;IACb;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,WAAW,OAAO,OAAO;YAC/B,MAAM,YAAY;YAClB,aAAa,WAAW;QAC1B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,qBACE;;0BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE;gBAAO;;;;;;0BAIlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,WAAU;gBACV,SAAS;gBACT,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,SAAS;oBACP,SAAS,YAAY,IAAI;oBACzB,OAAO,YAAY,IAAI;gBACzB;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,UAAU;oBAAE,OAAO;gBAAI;0BAEvB,cAAA,8OAAC;oBACC,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,OAAM;8BAEN,cAAA,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;;;;;0BAMrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE,CAAC,GAAG,CAAC,CAAC,qBACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAI;4BACzB,SAAS;gCACP,MAAM,UAAU,SAAS,cAAc,CAAC,CAAC,QAAQ,EAAE,MAAM;gCACzD,IAAI,SAAS;oCACX,QAAQ,cAAc,CAAC;wCAAE,UAAU;oCAAS;gCAC9C;4BACF;2BARK;;;;;;;;;;;;;;;0BAeb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,QAAQ;gCAAC;gCAAG;gCAAI;gCAAK;gCAAK;6BAAI;4BAC9B,SAAS;gCAAC;gCAAK;gCAAG;6BAAI;wBACxB;wBACA,YAAY;4BAAE,UAAU;4BAAG,QAAQ;4BAAU,MAAM;wBAAS;kCAC7D;;;;;;kCAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,QAAQ;gCAAC;gCAAK;gCAAK;gCAAK;gCAAI;6BAAE;4BAC9B,SAAS;gCAAC;gCAAK;gCAAG;6BAAI;wBACxB;wBACA,YAAY;4BAAE,UAAU;4BAAG,QAAQ;4BAAU,MAAM;4BAAU,OAAO;wBAAE;kCACvE;;;;;;kCAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,QAAQ;gCAAC;gCAAG;gCAAK;6BAAI;4BACrB,SAAS;gCAAC;gCAAK;gCAAG;6BAAI;wBACxB;wBACA,YAAY;4BAAE,UAAU;4BAAG,QAAQ;4BAAU,MAAM;4BAAU,OAAO;wBAAE;kCACvE;;;;;;kCAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,QAAQ;gCAAC;gCAAK;gCAAK;6BAAE;4BACrB,SAAS;gCAAC;gCAAK;gCAAG;6BAAI;wBACxB;wBACA,YAAY;4BAAE,UAAU;4BAAG,QAAQ;4BAAU,MAAM;4BAAU,OAAO;wBAAE;kCACvE;;;;;;;;;;;;;;AAMT;uCAEe", "debugId": null}}, {"offset": {"line": 1233, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/oi-portfolio/src/app/%28main%29/page.tsx"], "sourcesContent": ["'use client';\n\nimport { Suspense } from 'react';\nimport Hero from '@/components/Hero';\nimport Navigation from '@/components/Navigation';\nimport Scene3D from '@/components/Scene3D';\nimport ScrollIndicator from '@/components/ScrollIndicator';\n\nexport default function Home() {\n  return (\n    <div className=\"relative min-h-screen bg-white text-black overflow-hidden\">\n      <Navigation />\n\n      <main className=\"relative\">\n        <Hero />\n\n        <Suspense fallback={<div className=\"h-screen bg-white\" />}>\n          <Scene3D />\n        </Suspense>\n\n        <ScrollIndicator />\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,UAAU;;;;;0BAEX,8OAAC;gBAAK,WAAU;;kCACd,8OAAC,0HAAA,CAAA,UAAI;;;;;kCAEL,8OAAC,qMAAA,CAAA,WAAQ;wBAAC,wBAAU,8OAAC;4BAAI,WAAU;;;;;;kCACjC,cAAA,8OAAC,6HAAA,CAAA,UAAO;;;;;;;;;;kCAGV,8OAAC,qIAAA,CAAA,UAAe;;;;;;;;;;;;;;;;;AAIxB", "debugId": null}}]}