'use client';

import { Suspense, useRef, useMemo } from 'react';
import { <PERSON><PERSON>, use<PERSON>rame, useThree } from '@react-three/fiber';
import { 
  OrbitControls, 
  Environment, 
  Float, 
  MeshTransmissionMaterial,
  useTexture,
  Sphere,
  Box,
  Cylinder
} from '@react-three/drei';
import { Physics, RigidBody, CuboidCollider } from '@react-three/rapier';
import { <PERSON>Compose<PERSON>, <PERSON>, ToneMapping } from '@react-three/postprocessing';
import * as THREE from 'three';
import { useGesture } from '@use-gesture/react';

// Interactive 3D Objects Component
function Interactive3DObjects() {
  const groupRef = useRef<THREE.Group>(null);
  const { viewport, mouse } = useThree();
  
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = mouse.x * 0.1;
      groupRef.current.rotation.x = mouse.y * 0.05;
    }
  });

  const objects = useMemo(() => {
    const items = [];
    for (let i = 0; i < 15; i++) {
      items.push({
        id: i,
        position: [
          (Math.random() - 0.5) * 8,
          (Math.random() - 0.5) * 6,
          (Math.random() - 0.5) * 4
        ] as [number, number, number],
        rotation: [
          Math.random() * Math.PI,
          Math.random() * Math.PI,
          Math.random() * Math.PI
        ] as [number, number, number],
        scale: 0.3 + Math.random() * 0.7,
        type: Math.floor(Math.random() * 3), // 0: sphere, 1: box, 2: cylinder
        color: Math.random() > 0.5 ? '#000000' : '#ffffff'
      });
    }
    return items;
  }, []);

  return (
    <group ref={groupRef}>
      {objects.map((obj) => (
        <RigidBody key={obj.id} type="dynamic" position={obj.position}>
          <Float
            speed={1 + Math.random() * 2}
            rotationIntensity={0.5}
            floatIntensity={0.5}
          >
            {obj.type === 0 && (
              <Sphere args={[obj.scale]} castShadow receiveShadow>
                <MeshTransmissionMaterial
                  color={obj.color}
                  thickness={0.5}
                  roughness={0.1}
                  transmission={0.9}
                  ior={1.5}
                  chromaticAberration={0.02}
                  backside
                />
              </Sphere>
            )}
            {obj.type === 1 && (
              <Box args={[obj.scale, obj.scale, obj.scale]} castShadow receiveShadow>
                <meshStandardMaterial
                  color={obj.color}
                  metalness={0.8}
                  roughness={0.2}
                />
              </Box>
            )}
            {obj.type === 2 && (
              <Cylinder args={[obj.scale * 0.5, obj.scale * 0.5, obj.scale * 1.5]} castShadow receiveShadow>
                <meshStandardMaterial
                  color={obj.color}
                  metalness={0.6}
                  roughness={0.3}
                />
              </Cylinder>
            )}
          </Float>
        </RigidBody>
      ))}
    </group>
  );
}

// Mouse Follower Component
function MouseFollower() {
  const meshRef = useRef<THREE.Mesh>(null);
  const { mouse, viewport } = useThree();
  
  useFrame(() => {
    if (meshRef.current) {
      meshRef.current.position.x = (mouse.x * viewport.width) / 2;
      meshRef.current.position.y = (mouse.y * viewport.height) / 2;
    }
  });

  return (
    <mesh ref={meshRef} position={[0, 0, 2]}>
      <sphereGeometry args={[0.1]} />
      <meshBasicMaterial color="#ff0000" transparent opacity={0.5} />
    </mesh>
  );
}

// Main Scene Component
function Scene() {
  return (
    <>
      <ambientLight intensity={0.4} />
      <directionalLight
        position={[10, 10, 5]}
        intensity={1}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      <pointLight position={[-10, -10, -10]} intensity={0.5} />
      
      <Physics gravity={[0, -2, 0]}>
        <Interactive3DObjects />
        
        {/* Invisible floor */}
        <RigidBody type="fixed" position={[0, -4, 0]}>
          <CuboidCollider args={[10, 0.1, 10]} />
        </RigidBody>
      </Physics>
      
      <MouseFollower />
      
      <Environment preset="studio" />
      
      <OrbitControls
        enablePan={false}
        enableZoom={false}
        enableRotate={true}
        autoRotate
        autoRotateSpeed={0.5}
        maxPolarAngle={Math.PI / 2}
        minPolarAngle={Math.PI / 3}
      />
    </>
  );
}

// Post-processing Effects
function Effects() {
  return (
    <EffectComposer>
      <Bloom
        intensity={0.5}
        luminanceThreshold={0.9}
        luminanceSmoothing={0.9}
      />
      <ToneMapping adaptive />
    </EffectComposer>
  );
}

// Main Scene3D Component
const Scene3D = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const bind = useGesture({
    onMove: ({ xy: [x, y] }) => {
      // Mouse movement effects can be added here
    },
    onHover: ({ hovering }) => {
      if (canvasRef.current) {
        canvasRef.current.style.cursor = hovering ? 'pointer' : 'default';
      }
    }
  });

  return (
    <div 
      className="fixed inset-0 z-0" 
      {...bind()}
    >
      <Canvas
        ref={canvasRef}
        shadows
        camera={{ position: [0, 0, 8], fov: 50 }}
        gl={{ 
          antialias: true,
          alpha: true,
          powerPreference: 'high-performance'
        }}
      >
        <Suspense fallback={null}>
          <Scene />
          <Effects />
        </Suspense>
      </Canvas>
    </div>
  );
};

export default Scene3D;
