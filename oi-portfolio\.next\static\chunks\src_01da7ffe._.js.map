{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/oi-portfolio/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { motion, useScroll, useTransform } from 'framer-motion';\nimport { gsap } from 'gsap';\n\nconst Hero = () => {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const textRef = useRef<HTMLDivElement>(null);\n  const { scrollYProgress } = useScroll({\n    target: containerRef,\n    offset: ['start start', 'end start']\n  });\n\n  const y = useTransform(scrollYProgress, [0, 1], ['0%', '50%']);\n  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);\n\n  useEffect(() => {\n    if (textRef.current) {\n      const chars = textRef.current.querySelectorAll('.char');\n      \n      gsap.fromTo(chars, \n        { \n          y: 100,\n          opacity: 0,\n          rotationX: -90\n        },\n        {\n          y: 0,\n          opacity: 1,\n          rotationX: 0,\n          duration: 1.2,\n          stagger: 0.02,\n          ease: 'power3.out',\n          delay: 0.5\n        }\n      );\n    }\n  }, []);\n\n  const splitText = (text: string) => {\n    return text.split('').map((char, index) => (\n      <span key={index} className=\"char inline-block\" style={{ transformOrigin: '50% 100%' }}>\n        {char === ' ' ? '\\u00A0' : char}\n      </span>\n    ));\n  };\n\n  return (\n    <motion.section\n      ref={containerRef}\n      className=\"relative h-screen flex items-center justify-center overflow-hidden\"\n      style={{ y, opacity }}\n    >\n      {/* Background gradient */}\n      <div className=\"absolute inset-0 bg-gradient-to-b from-white via-gray-50 to-white\" />\n      \n      {/* Main content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-6 text-center\">\n        <motion.div\n          ref={textRef}\n          className=\"mb-8\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.6, delay: 0.3 }}\n        >\n          <h1 className=\"text-6xl md:text-8xl lg:text-9xl font-bold tracking-tight leading-none mb-6\">\n            {splitText('OI')}\n          </h1>\n          \n          <motion.p\n            className=\"text-xl md:text-2xl lg:text-3xl font-light max-w-4xl mx-auto leading-relaxed\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 1.2 }}\n          >\n            We help brands create digital experiences that connect with their audience\n          </motion.p>\n        </motion.div>\n\n        {/* Scroll indicator */}\n        <motion.div\n          className=\"absolute bottom-12 left-1/2 transform -translate-x-1/2\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 2 }}\n        >\n          <div className=\"flex flex-col items-center space-y-2\">\n            <span className=\"text-sm font-medium tracking-wider\">SCROLL TO EXPLORE</span>\n            <motion.div\n              className=\"w-px h-12 bg-black\"\n              animate={{ scaleY: [1, 0.5, 1] }}\n              transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}\n            />\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Decorative elements */}\n      <motion.div\n        className=\"absolute top-1/4 left-12 w-2 h-2 bg-black rounded-full\"\n        animate={{ \n          scale: [1, 1.5, 1],\n          opacity: [0.3, 1, 0.3]\n        }}\n        transition={{ duration: 3, repeat: Infinity, ease: 'easeInOut' }}\n      />\n      \n      <motion.div\n        className=\"absolute top-1/3 right-16 w-1 h-1 bg-black rounded-full\"\n        animate={{ \n          scale: [1, 2, 1],\n          opacity: [0.5, 1, 0.5]\n        }}\n        transition={{ duration: 4, repeat: Infinity, ease: 'easeInOut', delay: 1 }}\n      />\n      \n      <motion.div\n        className=\"absolute bottom-1/4 left-1/4 w-1.5 h-1.5 bg-black rounded-full\"\n        animate={{ \n          scale: [1, 1.8, 1],\n          opacity: [0.4, 1, 0.4]\n        }}\n        transition={{ duration: 2.5, repeat: Infinity, ease: 'easeInOut', delay: 0.5 }}\n      />\n    </motion.section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAMA,MAAM,OAAO;;IACX,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACvC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAe;SAAY;IACtC;IAEA,MAAM,IAAI,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAM;KAAM;IAC7D,MAAM,UAAU,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAI,EAAE;QAAC;QAAG;KAAE;IAE9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,QAAQ,OAAO,EAAE;gBACnB,MAAM,QAAQ,QAAQ,OAAO,CAAC,gBAAgB,CAAC;gBAE/C,gJAAA,CAAA,OAAI,CAAC,MAAM,CAAC,OACV;oBACE,GAAG;oBACH,SAAS;oBACT,WAAW,CAAC;gBACd,GACA;oBACE,GAAG;oBACH,SAAS;oBACT,WAAW;oBACX,UAAU;oBACV,SAAS;oBACT,MAAM;oBACN,OAAO;gBACT;YAEJ;QACF;yBAAG,EAAE;IAEL,MAAM,YAAY,CAAC;QACjB,OAAO,KAAK,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM,sBAC/B,6LAAC;gBAAiB,WAAU;gBAAoB,OAAO;oBAAE,iBAAiB;gBAAW;0BAClF,SAAS,MAAM,WAAW;eADlB;;;;;IAIf;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;QACb,KAAK;QACL,WAAU;QACV,OAAO;YAAE;YAAG;QAAQ;;0BAGpB,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,KAAK;wBACL,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,6LAAC;gCAAG,WAAU;0CACX,UAAU;;;;;;0CAGb,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CACzC;;;;;;;;;;;;kCAMH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAE;kCAEtC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAqC;;;;;;8CACrD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,QAAQ;4CAAC;4CAAG;4CAAK;yCAAE;oCAAC;oCAC/B,YAAY;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAY;;;;;;;;;;;;;;;;;;;;;;;0BAOvE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;gBACxB;gBACA,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;gBAAY;;;;;;0BAGjE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAG;qBAAE;oBAChB,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;gBACxB;gBACA,YAAY;oBAAE,UAAU;oBAAG,QAAQ;oBAAU,MAAM;oBAAa,OAAO;gBAAE;;;;;;0BAG3E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC;wBAAK;wBAAG;qBAAI;gBACxB;gBACA,YAAY;oBAAE,UAAU;oBAAK,QAAQ;oBAAU,MAAM;oBAAa,OAAO;gBAAI;;;;;;;;;;;;AAIrF;GAzHM;;QAGwB,4KAAA,CAAA,YAAS;QAK3B,+KAAA,CAAA,eAAY;QACN,+KAAA,CAAA,eAAY;;;KATxB;uCA2HS", "debugId": null}}, {"offset": {"line": 308, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/oi-portfolio/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst Navigation = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const menuItems = [\n    { name: 'Work', href: '#work' },\n    { name: 'About', href: '#about' },\n    { name: 'Contact', href: '#contact' },\n  ];\n\n  return (\n    <>\n      <motion.nav\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          scrolled ? 'bg-white/80 backdrop-blur-md' : 'bg-transparent'\n        }`}\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        transition={{ duration: 0.6, ease: 'easeOut' }}\n      >\n        <div className=\"max-w-7xl mx-auto px-6 py-6 flex items-center justify-between\">\n          {/* Logo */}\n          <motion.div\n            className=\"text-2xl font-bold tracking-tight\"\n            whileHover={{ scale: 1.05 }}\n            transition={{ type: 'spring', stiffness: 400, damping: 10 }}\n          >\n            OI\n          </motion.div>\n\n          {/* Desktop Menu */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {menuItems.map((item) => (\n              <motion.a\n                key={item.name}\n                href={item.href}\n                className=\"text-sm font-medium hover:text-gray-600 transition-colors\"\n                whileHover={{ y: -2 }}\n                transition={{ type: 'spring', stiffness: 400, damping: 10 }}\n              >\n                {item.name}\n              </motion.a>\n            ))}\n          </div>\n\n          {/* Let's Talk Button */}\n          <motion.button\n            className=\"hidden md:flex items-center space-x-2 bg-black text-white px-6 py-3 rounded-full text-sm font-medium hover:bg-gray-800 transition-colors\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            transition={{ type: 'spring', stiffness: 400, damping: 10 }}\n          >\n            <span>LET'S TALK</span>\n            <svg\n              width=\"16\"\n              height=\"16\"\n              viewBox=\"0 0 16 16\"\n              fill=\"none\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n            >\n              <path\n                d=\"M8 1L15 8L8 15M15 8H1\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n              />\n            </svg>\n          </motion.button>\n\n          {/* Mobile Menu Button */}\n          <motion.button\n            className=\"md:hidden flex flex-col items-center justify-center w-8 h-8 space-y-1\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            whileTap={{ scale: 0.95 }}\n          >\n            <motion.span\n              className=\"w-6 h-0.5 bg-black transition-all\"\n              animate={{\n                rotate: isMenuOpen ? 45 : 0,\n                y: isMenuOpen ? 6 : 0,\n              }}\n            />\n            <motion.span\n              className=\"w-6 h-0.5 bg-black transition-all\"\n              animate={{\n                opacity: isMenuOpen ? 0 : 1,\n              }}\n            />\n            <motion.span\n              className=\"w-6 h-0.5 bg-black transition-all\"\n              animate={{\n                rotate: isMenuOpen ? -45 : 0,\n                y: isMenuOpen ? -6 : 0,\n              }}\n            />\n          </motion.button>\n        </div>\n      </motion.nav>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMenuOpen && (\n          <motion.div\n            className=\"fixed inset-0 z-40 bg-white md:hidden\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            <div className=\"flex flex-col items-center justify-center h-full space-y-8\">\n              {menuItems.map((item, index) => (\n                <motion.a\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-2xl font-medium\"\n                  onClick={() => setIsMenuOpen(false)}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                >\n                  {item.name}\n                </motion.a>\n              ))}\n              <motion.button\n                className=\"bg-black text-white px-8 py-4 rounded-full text-lg font-medium\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                LET'S TALK\n              </motion.button>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAKA,MAAM,aAAa;;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;gBAC/B;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,MAAM,YAAY;QAChB;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE;;0BACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,AAAC,+DAEX,OADC,WAAW,iCAAiC;gBAE9C,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAU;0BAE7C,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,YAAY;gCAAE,MAAM;gCAAU,WAAW;gCAAK,SAAS;4BAAG;sCAC3D;;;;;;sCAKD,6LAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,YAAY;wCAAE,MAAM;wCAAU,WAAW;wCAAK,SAAS;oCAAG;8CAEzD,KAAK,IAAI;mCANL,KAAK,IAAI;;;;;;;;;;sCAYpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,YAAY;gCAAE,MAAM;gCAAU,WAAW;gCAAK,SAAS;4BAAG;;8CAE1D,6LAAC;8CAAK;;;;;;8CACN,6LAAC;oCACC,OAAM;oCACN,QAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,OAAM;8CAEN,cAAA,6LAAC;wCACC,GAAE;wCACF,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;;;;;;;;;;;;;;;;sCAMrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;4BAC9B,UAAU;gCAAE,OAAO;4BAAK;;8CAExB,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,WAAU;oCACV,SAAS;wCACP,QAAQ,aAAa,KAAK;wCAC1B,GAAG,aAAa,IAAI;oCACtB;;;;;;8CAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,WAAU;oCACV,SAAS;wCACP,SAAS,aAAa,IAAI;oCAC5B;;;;;;8CAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,WAAU;oCACV,SAAS;wCACP,QAAQ,aAAa,CAAC,KAAK;wCAC3B,GAAG,aAAa,CAAC,IAAI;oCACvB;;;;;;;;;;;;;;;;;;;;;;;0BAOR,6LAAC,4LAAA,CAAA,kBAAe;0BACb,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;8CAEhC,KAAK,IAAI;mCARL,KAAK,IAAI;;;;;0CAWlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;0CAC1B;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAnJM;KAAA;uCAqJS", "debugId": null}}, {"offset": {"line": 604, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/oi-portfolio/src/app/%28main%29/page.tsx"], "sourcesContent": ["'use client';\n\nimport { Suspense } from 'react';\nimport Hero from '@/components/Hero';\nimport Navigation from '@/components/Navigation';\nimport Scene3D from '@/components/Scene3D';\nimport ScrollIndicator from '@/components/ScrollIndicator';\n\nexport default function Home() {\n  return (\n    <div className=\"relative min-h-screen bg-white text-black overflow-hidden\">\n      <Navigation />\n\n      <main className=\"relative\">\n        <Hero />\n\n        <Suspense fallback={<div className=\"h-screen bg-white\" />}>\n          <Scene3D />\n        </Suspense>\n\n        <ScrollIndicator />\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;;;;;;;;;AAJA;;;;;;;AAQe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,UAAU;;;;;0BAEX,6LAAC;gBAAK,WAAU;;kCACd,6LAAC,6HAAA,CAAA,UAAI;;;;;kCAEL,6LAAC,6JAAA,CAAA,WAAQ;wBAAC,wBAAU,6LAAC;4BAAI,WAAU;;;;;;kCACjC,cAAA,6LAAC;;;;;;;;;;kCAGH,6LAAC;;;;;;;;;;;;;;;;;AAIT;KAhBwB", "debugId": null}}]}