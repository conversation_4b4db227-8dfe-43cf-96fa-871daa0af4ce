{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/oi-portfolio/src/app/%28main%29/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport localFont from \"next/font/local\";\nimport \"./globals.css\";\n\nconst aeonik = localFont({\n  src: [\n    {\n      path: \"../../../public/fonts/Aeonik-Medium.woff2\",\n      weight: \"500\",\n      style: \"normal\",\n    },\n  ],\n  variable: \"--font-aeonik\",\n  display: \"swap\",\n});\n\nexport const metadata: Metadata = {\n  title: \"OI - Digital Experiences\",\n  description: \"We help brands create digital experiences that connect with their audience\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" className={aeonik.variable}>\n      <body className=\"font-aeonik antialiased bg-white text-black\">\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAgBO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAW,CAAA,KAAA,CAAK,EAAE,QAAQ;kBACxC,cAAA,8OAAC;YAAK,WAAU;sBACb;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/oi-portfolio/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}