'use client';

import { useEffect, useState } from 'react';
import { motion, useScroll, useSpring } from 'framer-motion';

const ScrollIndicator = () => {
  const [isVisible, setIsVisible] = useState(false);
  const { scrollYProgress } = useScroll();
  const scaleX = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001
  });

  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.scrollY;
      const threshold = 100;
      setIsVisible(scrolled > threshold);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <>
      {/* Progress bar */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-black origin-left z-50"
        style={{ scaleX }}
      />

      {/* Scroll to top button */}
      <motion.button
        className="fixed bottom-8 right-8 w-12 h-12 bg-black text-white rounded-full flex items-center justify-center z-40 hover:bg-gray-800 transition-colors"
        onClick={scrollToTop}
        initial={{ opacity: 0, scale: 0 }}
        animate={{ 
          opacity: isVisible ? 1 : 0,
          scale: isVisible ? 1 : 0
        }}
        transition={{ duration: 0.3 }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M8 15V1M1 8L8 1L15 8"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </motion.button>

      {/* Side indicators */}
      <div className="fixed left-8 top-1/2 transform -translate-y-1/2 z-40 hidden lg:block">
        <div className="flex flex-col space-y-4">
          {[1, 2, 3, 4].map((item) => (
            <motion.div
              key={item}
              className="w-2 h-2 bg-black rounded-full cursor-pointer hover:scale-150 transition-transform"
              whileHover={{ scale: 1.5 }}
              onClick={() => {
                const section = document.getElementById(`section-${item}`);
                if (section) {
                  section.scrollIntoView({ behavior: 'smooth' });
                }
              }}
            />
          ))}
        </div>
      </div>

      {/* Plus signs decoration */}
      <div className="fixed inset-0 pointer-events-none z-10">
        <motion.div
          className="absolute top-1/4 left-12 text-2xl font-light"
          animate={{ 
            rotate: [0, 90, 180, 270, 360],
            opacity: [0.3, 1, 0.3]
          }}
          transition={{ duration: 8, repeat: Infinity, ease: 'linear' }}
        >
          +
        </motion.div>
        
        <motion.div
          className="absolute top-1/3 right-16 text-xl font-light"
          animate={{ 
            rotate: [360, 270, 180, 90, 0],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{ duration: 6, repeat: Infinity, ease: 'linear', delay: 2 }}
        >
          +
        </motion.div>
        
        <motion.div
          className="absolute bottom-1/4 left-1/4 text-lg font-light"
          animate={{ 
            rotate: [0, 180, 360],
            opacity: [0.4, 1, 0.4]
          }}
          transition={{ duration: 4, repeat: Infinity, ease: 'linear', delay: 1 }}
        >
          +
        </motion.div>
        
        <motion.div
          className="absolute bottom-1/3 right-1/3 text-sm font-light"
          animate={{ 
            rotate: [360, 180, 0],
            opacity: [0.6, 1, 0.6]
          }}
          transition={{ duration: 5, repeat: Infinity, ease: 'linear', delay: 3 }}
        >
          +
        </motion.div>
      </div>
    </>
  );
};

export default ScrollIndicator;
