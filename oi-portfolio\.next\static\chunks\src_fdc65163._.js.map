{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/oi-portfolio/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst Navigation = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const menuItems = [\n    { name: 'Work', href: '#work' },\n    { name: 'About', href: '#about' },\n    { name: 'Contact', href: '#contact' },\n  ];\n\n  return (\n    <>\n      <motion.nav\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          scrolled ? 'bg-white/80 backdrop-blur-md' : 'bg-transparent'\n        }`}\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        transition={{ duration: 0.6, ease: 'easeOut' }}\n      >\n        <div className=\"max-w-7xl mx-auto px-6 py-6 flex items-center justify-between\">\n          {/* Logo */}\n          <motion.div\n            className=\"text-2xl font-bold tracking-tight\"\n            whileHover={{ scale: 1.05 }}\n            transition={{ type: 'spring', stiffness: 400, damping: 10 }}\n          >\n            OI\n          </motion.div>\n\n          {/* Desktop Menu */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {menuItems.map((item) => (\n              <motion.a\n                key={item.name}\n                href={item.href}\n                className=\"text-sm font-medium hover:text-gray-600 transition-colors\"\n                whileHover={{ y: -2 }}\n                transition={{ type: 'spring', stiffness: 400, damping: 10 }}\n              >\n                {item.name}\n              </motion.a>\n            ))}\n          </div>\n\n          {/* Let's Talk Button */}\n          <motion.button\n            className=\"hidden md:flex items-center space-x-2 bg-black text-white px-6 py-3 rounded-full text-sm font-medium hover:bg-gray-800 transition-colors\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            transition={{ type: 'spring', stiffness: 400, damping: 10 }}\n          >\n            <span>LET'S TALK</span>\n            <svg\n              width=\"16\"\n              height=\"16\"\n              viewBox=\"0 0 16 16\"\n              fill=\"none\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n            >\n              <path\n                d=\"M8 1L15 8L8 15M15 8H1\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n              />\n            </svg>\n          </motion.button>\n\n          {/* Mobile Menu Button */}\n          <motion.button\n            className=\"md:hidden flex flex-col items-center justify-center w-8 h-8 space-y-1\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            whileTap={{ scale: 0.95 }}\n          >\n            <motion.span\n              className=\"w-6 h-0.5 bg-black transition-all\"\n              animate={{\n                rotate: isMenuOpen ? 45 : 0,\n                y: isMenuOpen ? 6 : 0,\n              }}\n            />\n            <motion.span\n              className=\"w-6 h-0.5 bg-black transition-all\"\n              animate={{\n                opacity: isMenuOpen ? 0 : 1,\n              }}\n            />\n            <motion.span\n              className=\"w-6 h-0.5 bg-black transition-all\"\n              animate={{\n                rotate: isMenuOpen ? -45 : 0,\n                y: isMenuOpen ? -6 : 0,\n              }}\n            />\n          </motion.button>\n        </div>\n      </motion.nav>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMenuOpen && (\n          <motion.div\n            className=\"fixed inset-0 z-40 bg-white md:hidden\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            <div className=\"flex flex-col items-center justify-center h-full space-y-8\">\n              {menuItems.map((item, index) => (\n                <motion.a\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-2xl font-medium\"\n                  onClick={() => setIsMenuOpen(false)}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                >\n                  {item.name}\n                </motion.a>\n              ))}\n              <motion.button\n                className=\"bg-black text-white px-8 py-4 rounded-full text-lg font-medium\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                LET'S TALK\n              </motion.button>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAKA,MAAM,aAAa;;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;gBAC/B;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,MAAM,YAAY;QAChB;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE;;0BACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,AAAC,+DAEX,OADC,WAAW,iCAAiC;gBAE9C,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAU;0BAE7C,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,YAAY;gCAAE,MAAM;gCAAU,WAAW;gCAAK,SAAS;4BAAG;sCAC3D;;;;;;sCAKD,6LAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,YAAY;wCAAE,GAAG,CAAC;oCAAE;oCACpB,YAAY;wCAAE,MAAM;wCAAU,WAAW;wCAAK,SAAS;oCAAG;8CAEzD,KAAK,IAAI;mCANL,KAAK,IAAI;;;;;;;;;;sCAYpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,YAAY;gCAAE,MAAM;gCAAU,WAAW;gCAAK,SAAS;4BAAG;;8CAE1D,6LAAC;8CAAK;;;;;;8CACN,6LAAC;oCACC,OAAM;oCACN,QAAO;oCACP,SAAQ;oCACR,MAAK;oCACL,OAAM;8CAEN,cAAA,6LAAC;wCACC,GAAE;wCACF,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;;;;;;;;;;;;;;;;sCAMrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;4BAC9B,UAAU;gCAAE,OAAO;4BAAK;;8CAExB,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,WAAU;oCACV,SAAS;wCACP,QAAQ,aAAa,KAAK;wCAC1B,GAAG,aAAa,IAAI;oCACtB;;;;;;8CAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,WAAU;oCACV,SAAS;wCACP,SAAS,aAAa,IAAI;oCAC5B;;;;;;8CAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,WAAU;oCACV,SAAS;wCACP,QAAQ,aAAa,CAAC,KAAK;wCAC3B,GAAG,aAAa,CAAC,IAAI;oCACvB;;;;;;;;;;;;;;;;;;;;;;;0BAOR,6LAAC,4LAAA,CAAA,kBAAe;0BACb,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;8CAEhC,KAAK,IAAI;mCARL,KAAK,IAAI;;;;;0CAWlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;0CAC1B;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAnJM;KAAA;uCAqJS", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/oi-portfolio/src/app/%28main%29/page.tsx"], "sourcesContent": ["'use client';\n\nimport { Suspense } from 'react';\nimport Hero from '@/components/Hero';\nimport Navigation from '@/components/Navigation';\nimport Scene3D from '@/components/Scene3D';\nimport ScrollIndicator from '@/components/ScrollIndicator';\n\nexport default function Home() {\n  return (\n    <div className=\"relative min-h-screen bg-white text-black overflow-hidden\">\n      <Navigation />\n\n      <main className=\"relative\">\n        <Hero />\n\n        <Suspense fallback={<div className=\"h-screen bg-white\" />}>\n          <Scene3D />\n        </Suspense>\n\n        <ScrollIndicator />\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;;;;AAEA;;;;;;;;;;;AAJA;;;;;;;AAQe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,UAAU;;;;;0BAEX,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;;;;;kCAED,6LAAC,6JAAA,CAAA,WAAQ;wBAAC,wBAAU,6LAAC;4BAAI,WAAU;;;;;;kCACjC,cAAA,6LAAC;;;;;;;;;;kCAGH,6LAAC;;;;;;;;;;;;;;;;;AAIT;KAhBwB", "debugId": null}}]}