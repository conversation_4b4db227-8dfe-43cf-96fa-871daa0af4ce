'use client';

import { Suspense } from 'react';
import Hero from '@/components/Hero';
import Navigation from '@/components/Navigation';
import Scene3D from '@/components/Scene3D';
import ScrollIndicator from '@/components/ScrollIndicator';
import ContentSection from '@/components/ContentSection';

export default function Home() {
  return (
    <div className="relative min-h-screen bg-white text-black overflow-hidden">
      <Navigation />

      <main className="relative">
        <Hero />

        <ContentSection />

        <Suspense fallback={<div className="h-screen bg-white" />}>
          <Scene3D />
        </Suspense>

        <ScrollIndicator />
      </main>
    </div>
  );
}
