{"name": "oi-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@lottiefiles/react-lottie-player": "^3.6.0", "@react-three/a11y": "^3.0.0", "@react-three/drei": "^9.79.2", "@react-three/fiber": "^9.2.0", "@react-three/postprocessing": "^3.0.4", "@react-three/rapier": "^2.1.0", "@studio-freight/react-lenis": "^0.0.47", "@use-gesture/react": "^10.3.1", "@vercel/analytics": "^1.5.0", "framer-motion": "^12.23.6", "gsap": "^3.13.0", "lenis": "^1.3.5", "locomotive-scroll": "^4.1.4", "next": "15.4.1", "react": "^18.2.0", "react-cursor-fx": "^1.0.2", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-spring": "^10.0.1", "react-use-measure": "^2.1.7", "three": "^0.178.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "typescript": "^5"}}