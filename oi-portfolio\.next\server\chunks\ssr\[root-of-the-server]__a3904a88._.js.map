{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/dm_sans_3cb857e9.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"dm_sans_3cb857e9-module__SKwaPa__className\",\n  \"variable\": \"dm_sans_3cb857e9-module__SKwaPa__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/dm_sans_3cb857e9.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22DM_Sans%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22500%22,%22600%22,%22700%22],%22variable%22:%22--font-dm-sans%22,%22display%22:%22swap%22}],%22variableName%22:%22dmSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'DM Sans', 'DM Sans Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,uJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,uJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,uJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/oi-portfolio/src/app/%28main%29/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport { DM_Sans } from \"next/font/google\";\nimport localFont from \"next/font/local\";\nimport \"./globals.css\";\n\n// Fallback to DM Sans (similar to Aeonik)\nconst dmSans = DM_Sans({\n  subsets: [\"latin\"],\n  weight: [\"400\", \"500\", \"600\", \"700\"],\n  variable: \"--font-dm-sans\",\n  display: \"swap\",\n});\n\n// Custom Aeonik font (fallback to DM Sans if not available)\nconst aeonik = localFont({\n  src: [\n    {\n      path: \"../../../public/fonts/Aeonik-Medium.woff2\",\n      weight: \"500\",\n      style: \"normal\",\n    },\n  ],\n  variable: \"--font-aeonik\",\n  display: \"swap\",\n  fallback: [\"var(--font-dm-sans)\", \"system-ui\", \"sans-serif\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"OI - Digital Experiences\",\n  description: \"We help brands create digital experiences that connect with their audience\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" className={`${dmSans.variable} ${aeonik.variable}`}>\n      <body className=\"antialiased bg-white text-black\" style={{ fontFamily: 'var(--font-dm-sans), system-ui, sans-serif' }}>\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;AA2BO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAW,GAAG,2IAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAA,KAAA,CAAK,EAAE,QAAQ,EAAE;kBAChE,cAAA,8OAAC;YAAK,WAAU;YAAkC,OAAO;gBAAE,YAAY;YAA6C;sBACjH;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/project/Nextjs/Work/oi-portfolio/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}