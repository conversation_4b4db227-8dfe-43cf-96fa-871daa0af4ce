@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #000000;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-aeonik: var(--font-aeonik);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-aeonik), var(--font-dm-sans), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow-x: hidden;
  line-height: 1.6;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #000;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #333;
}

/* Smooth transitions */
* {
  transition: color 0.3s ease, background-color 0.3s ease;
}

/* Selection styles */
::selection {
  background: #000;
  color: #fff;
}

/* Focus styles */
button:focus,
a:focus {
  outline: 2px solid #000;
  outline-offset: 2px;
}
